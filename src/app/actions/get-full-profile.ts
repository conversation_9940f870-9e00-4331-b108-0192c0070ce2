"use server";

import { createClient } from "@/utils/supabase/server";

export async function getStudentProfileData() {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error("Error fetching user or no user logged in:", authError);
    return { success: false, data: null, error: "User not authenticated" };
  }

  try {
    const { data, error } = await supabase
      .from("student_profiles")
      .select("*") // Fetch all columns
      .eq("user_id", user.id)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        console.warn(
          "Student profile not found for user_id (PGRST116):",
          user.id,
          error.message
        );
        return { success: false, data: null, error: "Profile not found" };
      }
      console.error("Error fetching student profile:", error);
      return { success: false, data: null, error: error.message };
    }

    if (data) {
      return { success: true, data, error: null }; // Return full data object
    } else {
      console.warn(
        "Student profile not found (no data returned) for user_id:",
        user.id
      );
      return { success: false, data: null, error: "Profile not found" };
    }
  } catch (e: unknown) {
    console.error("Unexpected error fetching student profile:", e);
    const errorMessage =
      e instanceof Error ? e.message : "An unexpected error occurred";
    return { success: false, data: null, error: errorMessage };
  }
}