"use client";

import ProtectedRoute from "@/components/protected-route";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { getStudentProfileData } from "@/app/actions/get-full-profile";
import RoleBadge from "@/components/role-badge";
import { getUserRole, UserRole } from "@/utils/roles";
import Link from "next/link";

interface FullStudentProfile {
  name: string | null;
  grade: string | null;
  attention_span: string | null;
  engagement_level: string | null;
  modality_preference: string | null;
  challenge_preference: string | null;
  tutoring_style_preference: string | null;
  optimal_difficulty_level: number | null;
  collaboration_preference: string | null;
  communication_style: string | null;
  self_assessment_ability: string | null;
  global_correct_first_attempt_rate: number | null;
  global_skip_rate: number | null;
  age: number | null;
  languages: string | null;
  created_at?: string;
  updated_at?: string;
}

export default function ProfilePage() {
  const { user, signOut } = useAuth();
  const router = useRouter();

  const [profileData, setProfileData] = useState<FullStudentProfile | null>(
    null
  );
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasFetched, setHasFetched] = useState(false);

  useEffect(() => {
    if (!user || hasFetched) return;

    async function fetchData() {
      if (!user) return;
      setIsLoading(true);

      const role = await getUserRole();
      setUserRole(role);

      const result = await getStudentProfileData();
      if (result.success && result.data) {
        setProfileData(result.data as FullStudentProfile);
      } else {
        console.error("Failed to fetch full profile", result.error);
      }

      setIsLoading(false);
      setHasFetched(true);
    }

    fetchData();
  }, [user, hasFetched]);

  const handleSignOut = async () => {
    await signOut();
    router.push("/");
  };

  const displayName = isLoading
    ? "Loading..."
    : profileData?.name || user?.email;

  const excludeKeys = new Set([
    "ai_learning_patterns",
    "ai_recommendations",
    "user_id",
  ]);

  const formatField = (key: string, value: any) => {
    if (key === "languages") {
      if (Array.isArray(value)) {
        return value
          .map((lang) => lang.charAt(0).toUpperCase() + lang.slice(1))
          .join(", ");
      } else if (typeof value === "string") {
        return value
          .split(",")
          .map(
            (lang) => lang.trim().charAt(0).toUpperCase() + lang.trim().slice(1)
          )
          .join(", ");
      } else {
        return "N/A";
      }
    }

    if (key === "created_at" || key === "updated_at") {
      return value
        ? new Date(value).toLocaleDateString(undefined, {
            year: "numeric",
            month: "short",
            day: "numeric",
          })
        : "N/A";
    }

    return value !== null ? value.toString() : "N/A";
  };

  const labelMap: Record<string, string> = {
    name: "Name",
    grade: "Grade Level",
    attention_span: "Attention Span",
    engagement_level: "Engagement Level",
    modality_preference: "Learning Style",
    challenge_preference: "Challenge Preference",
    tutoring_style_preference: "Tutoring Style",
    optimal_difficulty_level: "Preferred Difficulty (1–10)",
    collaboration_preference: "Collaboration Preference",
    communication_style: "Communication Style",
    self_assessment_ability: "Self-Assessment Ability",
    global_correct_first_attempt_rate: "First Try Accuracy",
    global_skip_rate: "Skip Rate",
    age: "Age",
    languages: "Languages Spoken",
    created_at: "Created On",
    updated_at: "Last Updated",
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-100">
        {/* Header */}
        <header className="bg-blue-900 shadow-md">
          <div className="container mx-auto flex items-center justify-between p-4">
            <h1 className="text-white text-xl font-semibold">EdEngage</h1>
            <div className="flex items-center space-x-4">
              <span className="text-white">Welcome, {displayName}</span>
              <button
                onClick={handleSignOut}
                className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
              >
                Sign Out
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="container mx-auto p-8">
          <h1 className="mb-6 text-3xl font-bold">Student Profile</h1>

          <div className="rounded-lg bg-white p-6 shadow-lg">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">Profile Details</h2>
              {userRole && <RoleBadge role={userRole} />}
            </div>

            {isLoading ? (
              <p>Loading profile...</p>
            ) : profileData ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(profileData).map(([key, value]) => {
                  if (excludeKeys.has(key)) return null;

                  const label =
                    labelMap[key] ||
                    key
                      .replace(/_/g, " ")
                      .replace(/\b\w/g, (l) => l.toUpperCase());

                  return (
                    <div key={key} className="border-b pb-2">
                      <strong>{label}:</strong> {formatField(key, value)}
                    </div>
                  );
                })}
              </div>
            ) : (
              <p>Could not load profile information.</p>
            )}

            {!isLoading && profileData && (
              <div className="mt-6">
                <Link
                  href="/student/intake/basic-info"
                  className="text-blue-600 hover:underline"
                >
                  Edit Intake Form
                </Link>
              </div>
            )}
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
