"use client";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { supabase } from "@/utils/supabase/client";
import { getDefaultRedirectPath, type UserRole } from "@/lib/auth/auth-utils";

export default function Home() {
  const { user, userRole, isLoading, signOut } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [checkedAuth, setCheckedAuth] = useState(false);
  const [redirecting, setRedirecting] = useState(false);

  // Check for error messages from auth callback
  const error = searchParams.get("error");

  useEffect(() => {
    async function handleAuthenticatedUser() {
      console.log("Page.tsx - Auth state:", {
        isLoading,
        user: !!user,
        userRole,
        checkedAuth,
        redirecting,
      });

      if (!isLoading && user) {
        setRedirecting(true);

        try {
          // Get user role from database if not available in context
          let currentUserRole = userRole;
          if (!currentUserRole) {
            console.log("Fetching user role from database...");
            const { data: profile } = await supabase
              .from("users")
              .select("role")
              .eq("id", user.id)
              .maybeSingle();
            currentUserRole = profile?.role;
            console.log("User role from DB:", currentUserRole);
          }

          if (currentUserRole === "student") {
            // Check if student has completed intake
            const { data: studentProfile } = await supabase
              .from("student_profiles")
              .select("name, grade")
              .eq("user_id", user.id)
              .maybeSingle();

            if (
              !studentProfile ||
              !studentProfile.name ||
              !studentProfile.grade
            ) {
              // Student hasn't completed intake, redirect to intake form
              setCheckedAuth(true);
              router.push("/student/intake/start");
            } else {
              // Student has completed intake, redirect to dashboard
              setCheckedAuth(true);
              router.push("/student/dashboard");
            }
          } else if (currentUserRole) {
            // For teachers and admins, redirect to their respective dashboards
            const redirectPath = getDefaultRedirectPath(
              currentUserRole as UserRole
            );
            setCheckedAuth(true);
            router.push(redirectPath);
          } else {
            // No role found - user exists in Supabase Auth but not in database
            // This happens when user was deleted from database but session is still valid
            console.warn(
              "User exists in auth but not in database. Signing out user:",
              user.id
            );
            await signOut();
            setCheckedAuth(true);
            setRedirecting(false);
            return; // Stay on home page after sign out
          }
        } catch (error) {
          console.error("Error checking user profile:", error);
          // On error, also sign out the user to prevent infinite loops
          console.warn(
            "Database error - signing out user to prevent redirect loops"
          );
          await signOut();
          setCheckedAuth(true);
          setRedirecting(false);
          return; // Stay on home page after sign out
        }
      } else if (!isLoading) {
        setCheckedAuth(true);
      }
    }

    handleAuthenticatedUser();
  }, [isLoading, user, userRole, router, signOut]);

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isLoading || !checkedAuth) {
        console.warn("Auth check taking too long, forcing checkedAuth to true");
        setCheckedAuth(true);
        setRedirecting(false);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(timeout);
  }, [isLoading, checkedAuth]);

  // While checking auth or redirecting, show spinner
  if (isLoading || !checkedAuth || redirecting) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {redirecting ? "Redirecting to your dashboard..." : "Loading..."}
          </p>
          <p className="text-xs text-gray-400 mt-2">
            Debug: isLoading={isLoading.toString()}, checkedAuth=
            {checkedAuth.toString()}, redirecting={redirecting.toString()}
          </p>
        </div>
      </div>
    );
  }

  return (
    <main className="relative min-h-screen bg-gray-100">
      {/* Logo top left */}
      <div className="absolute left-8 top-8">
        <button className="logoButton">EdEngage</button>
      </div>

      {/* Main content center */}
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="w-full max-w-2xl rounded-lg bg-white p-8 shadow-md">
          <div className="flex flex-col items-center text-center">
            <h1 className="mb-4 text-5xl font-bold text-blue-500">Welcome</h1>
            <h2 className="mb-8 text-2xl font-semibold text-gray-700">
              EdEngage Product Demo
            </h2>

            {/* Error message display */}
            {error && (
              <div className="mb-6 rounded-md bg-red-50 border border-red-200 p-4 max-w-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">
                      {error === "account_not_found" &&
                        "Your account was not found. Please contact support or register for a new account."}
                      {error === "database_error" &&
                        "A technical error occurred. Please try again later."}
                      {error === "profile_creation_failed" &&
                        "Unable to create your profile. Please try again later."}
                      {![
                        "account_not_found",
                        "database_error",
                        "profile_creation_failed",
                      ].includes(error) &&
                        "An error occurred during sign in. Please try again."}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <p className="mb-10 text-gray-500 max-w-md">
              Experience how AI can personalize learning for every student. Get
              started instantly!
            </p>
            <Link
              href="/auth/magic-link"
              className="flex flex-col items-center justify-center rounded-lg bg-blue-900 p-8 text-white transition-transform hover:scale-105 shadow-lg"
            >
              <div className="mb-4 flex h-20 w-20 items-center justify-center rounded bg-blue-800 p-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
              </div>
              <span className="text-xl font-bold">Sign in or Register</span>
              <span className="mt-2 text-sm text-gray-200">
                Receive a secure link in your inbox
              </span>
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
